import { theme } from "ant-design-vue";

// 预设主题类型Key
enum PreTheme {
  default = "default",
  dark = "dark",
  compact = "compact",
  custom = "custom",
}
// 存储localStorage的key
const storageKey = "app-theme";

// 默认的主题色：红色, 代表没有设置过
const defaultColor = "#1f74ec";

// 默认的主题算法映射
const algorithms = {
  default: theme.defaultAlgorithm,
  dark: theme.darkAlgorithm,
  compact: theme.compactAlgorithm,
  custom: theme.defaultAlgorithm, // 自定义主题基于默认算法，通过 token 定制
};

// 应用主题配置参数
interface ApplyThemeConfig {
  preThemeKey: PreTheme;
  color?: string;
}

class Manager {
  /** 预设主题类型Key:用于获取主题*/
  private preThemeKey: PreTheme = PreTheme.default;
  /** 主题颜色 */
  private color: string = defaultColor;
  /** antd主题，通过preTheme获取 */
  private algorithm = theme.defaultAlgorithm;

  constructor() {
    // 从 localStorage 恢复主题设置
    const storageThemeInfo = JSON.parse(
      localStorage.getItem(storageKey) || "{}"
    ) as ApplyThemeConfig;

    // 存在则更新，否则新增一个并放到浏览器缓存
    if (storageThemeInfo) {
      this.preThemeKey = storageThemeInfo.preThemeKey;
      this.color = storageThemeInfo.color || defaultColor;
    }
    const config: ApplyThemeConfig = {
      preThemeKey: PreTheme.default,
      color: defaultColor,
    };
    localStorage.setItem(storageKey, JSON.stringify(config));
  }

  /** 应用主题 */
  public applyTheme(config: ApplyThemeConfig) {
    this.preThemeKey = config.preThemeKey;
    this.color = config.color || this.color;
    this.algorithm = algorithms[this.preThemeKey] || this.algorithm;
  }

  /** 获取主题 */
  public getTheme() {
    return {
      preTheme: this.preThemeKey,
      color: this.color,
      algorithm: this.algorithm,
    };
  }


}
