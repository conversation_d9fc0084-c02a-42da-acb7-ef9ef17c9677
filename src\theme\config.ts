import { theme } from "ant-design-vue";
import type { ThemeConfig } from "ant-design-vue/es/config-provider/context";
import { ref, computed, onMounted, onUnmounted, type Ref } from "vue";

// 主题类型定义
export type ThemeType = "default" | "dark" | "compact" | "custom";

// 存储主题信息
const storageKey = "app-theme";

// 存储主题信息类型
interface StorageThemeInfo {
  // 主题类型
  themeType: ThemeType;
  // 自定义主题颜色
  customToken?: string;
}

// 应用主题配置
interface ApplyThemeConfig {
  themeType: ThemeType;
  token?: string;
}

// 默认的主题算法映射
const themeAlgorithms = {
  default: theme.defaultAlgorithm,
  dark: theme.darkAlgorithm,
  compact: theme.compactAlgorithm,
  custom: theme.defaultAlgorithm, // 自定义主题基于默认算法，通过 token 定制
};

// 默认主题 token
export const defaultTokens = {
  colorPrimary: "#1f74ec",
};

// 自定义主题 token
export const customTokens = {
  colorPrimary: "#6366f1",
  colorSuccess: "#52c41a",
  colorWarning: "#faad14",
  colorError: "#ff4d4f",
  colorInfo: "#1890ff",
};

// 主题配置生成器
export function getThemeConfig(themeType: ThemeType, customToken?: string): ThemeConfig {
  let primaryColor = defaultTokens.colorPrimary;
  
  if (themeType === "custom") {
    // 如果是自定义主题，优先使用传入的自定义颜色，否则使用默认的自定义颜色
    primaryColor = customToken || customTokens.colorPrimary;
  }
  
  const baseConfig: ThemeConfig = {
    token: {
      colorPrimary: primaryColor,
    },
    algorithm: themeAlgorithms[themeType],
  };
  return baseConfig;
}

// 主题状态管理
class ThemeManager {
  // 当前主题类型
  private currentTheme: ThemeType = "dark";
  // 自定义主题颜色
  private customToken?: string;
  private listeners: Array<(theme: ThemeType, customToken?: string) => void> = [];

  constructor() {
    // 从 localStorage 恢复主题设置
    const storageThemeInfo = JSON.parse(
      localStorage.getItem(storageKey) || "{}"
    ) as StorageThemeInfo;
    
    if (storageThemeInfo && this.isValidTheme(storageThemeInfo.themeType)) {
      this.currentTheme = storageThemeInfo.themeType;
      this.customToken = storageThemeInfo.customToken;
    } else {
      localStorage.setItem(
        storageKey,
        JSON.stringify({
          themeType: this.currentTheme,
        })
      );
    }
  }

  // 验证主题类型是否有效
  private isValidTheme(theme: string): theme is ThemeType {
    return ["default", "dark", "compact", "custom"].includes(theme);
  }

  // 获取当前主题类型
  getCurrentTheme(): ThemeType {
    return this.currentTheme;
  }

  // 获取当前自定义token
  getCurrentCustomToken(): string | undefined {
    return this.customToken;
  }

  // 获取当前主题配置
  getCurrentThemeConfig(): ThemeConfig {
    return getThemeConfig(this.currentTheme, this.customToken);
  }

  // 设置主题类型
  setTheme(applyThemeConfig: ApplyThemeConfig): void {
    this.currentTheme = applyThemeConfig.themeType;
    
    // 如果是自定义主题且提供了token，则更新自定义token
    if (applyThemeConfig.themeType === "custom" && applyThemeConfig.token) {
      this.customToken = applyThemeConfig.token;
    }

    // 将主题信息存储到localStorage
    const storageInfo: StorageThemeInfo = {
      themeType: applyThemeConfig.themeType,
    };
    
    if (this.customToken) {
      storageInfo.customToken = this.customToken;
    }
    
    localStorage.setItem(storageKey, JSON.stringify(storageInfo));
    
    // 通知主题变化
    this.notifyListeners();
  }

  // 订阅主题变化
  subscribe(listener: (theme: ThemeType, customToken?: string) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  // 通知主题变化
  private notifyListeners(): void {
    this.listeners.forEach((listener) => listener(this.currentTheme, this.customToken));
  }
}

// 导出单例实例
export const themeManager = new ThemeManager();

// 主题信息配置
export const themeInfo = {
  default: {
    name: "默认主题",
    description: "标准的 Ant Design 主题",
    preview: "linear-gradient(45deg, #1890ff, #52c41a)",
  },
  dark: {
    name: "暗色主题",
    description: "适合夜间使用的深色主题",
    preview: "linear-gradient(45deg, #434343, #262626)",
  },
  compact: {
    name: "紧凑主题",
    description: "更紧凑的布局和间距",
    preview: "linear-gradient(45deg, #1890ff, #722ed1)",
  },
  custom: {
    name: "自定义主题",
    description: "自定义品牌主题",
    preview: "linear-gradient(45deg, #6366f1, #8b5cf6)",
  },
} as const;

// 工具函数：获取主题信息
export function getThemeSettingInfo(themeType: ThemeType) {
  return themeInfo[themeType];
}

// 工具函数：获取所有可用主题
export function getAllThemes(): Array<{
  key: ThemeType;
  info: (typeof themeInfo)[ThemeType];
}> {
  return Object.entries(themeInfo).map(([key, info]) => ({
    key: key as ThemeType,
    info,
  }));
}

// 工具函数：切换主题
export function applyTheme(applyThemeConfig: ApplyThemeConfig): void {
  console.log("应用主题配置:", applyThemeConfig);
  themeManager.setTheme(applyThemeConfig);
}

// 工具函数：获取当前主题
export function getCurrentTheme(): ThemeType {
  return themeManager.getCurrentTheme();
}

// 工具函数：获取当前自定义token
export function getCurrentCustomToken(): string | undefined {
  return themeManager.getCurrentCustomToken();
}

// 工具函数：获取当前主题配置
export function getCurrentThemeConfig(): ThemeConfig {
  return themeManager.getCurrentThemeConfig();
}

// 扩展接口：创建自定义主题配置
export function createCustomThemeConfig(
  baseTheme: ThemeType = "default",
  customToken?: string
): ThemeConfig {
  const baseConfig = getThemeConfig(baseTheme, customToken);

  // 合并自定义 token
  const mergedTokens = {
    ...baseConfig.token,
    ...customTokens,
  };

  return {
    ...baseConfig,
    token: mergedTokens,
  };
}

// Vue 组合式函数：响应式主题管理
export function useTheme() {
  const currentTheme: Ref<ThemeType> = ref(themeManager.getCurrentTheme());
  const currentCustomToken: Ref<string | undefined> = ref(themeManager.getCurrentCustomToken());
  const currentThemeConfig = computed(() => getThemeConfig(currentTheme.value, currentCustomToken.value));

  // 在组件内部使用 theme.useToken() 获取响应式的主题token
  const { token } = theme.useToken();

  // 响应式地根据当前主题token计算颜色信息
  const currentThemeSettingInfo = computed(() =>
    getThemeSettingInfo(currentTheme.value)
  );

  let unsubscribe: (() => void) | null = null;

  onMounted(() => {
    // 订阅主题变化
    unsubscribe = themeManager.subscribe((newTheme: ThemeType, newCustomToken?: string) => {
      currentTheme.value = newTheme;
      currentCustomToken.value = newCustomToken;
      console.log("主题变化:", newTheme, "自定义颜色:", newCustomToken);
    });
  });

  onUnmounted(() => {
    // 清理订阅
    if (unsubscribe) {
      unsubscribe();
    }
  });

  return {
    currentTheme: computed(() => currentTheme.value),
    currentThemeConfig,
    currentThemeSettingInfo,
    currentCustomToken: computed(() => currentCustomToken.value),
    token: computed(() => token.value),
    getAllThemes,
  };
}
